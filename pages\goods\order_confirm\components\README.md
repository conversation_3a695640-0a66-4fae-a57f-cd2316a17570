# PriceDetail 价格明细组件

## 组件描述

PriceDetail 是一个用于显示订单价格明细的 UniApp 组件，严格按照 Figma 设计稿实现，包含商品金额、运费、各种抵扣项目和合计金额的展示。

## 功能特性

- 📊 完整的价格明细展示
- 🎨 严格按照 Figma 设计稿实现的 UI
- 🖼️ 支持图标展示（债权抵扣、现金券、佣金支付、添星积分）
- 🔗 支持更多按钮点击事件
- 📱 响应式设计，适配不同屏幕尺寸

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| productAmount | String/Number | '1924.50' | 商品金额 |
| shippingFee | String/Number | '3.50' | 运费 |
| debtDeduction | String/Number | '192.40' | 债权抵扣金额 |
| cashCoupon | String/Number | '0.0' | 现金券抵扣金额 |
| commissionPayment | String/Number | '0.0' | 佣金支付金额 |
| pointsDeduction | String/Number | '0.0' | 添星积分抵扣金额 |
| totalAmount | String/Number | '1735.60' | 合计金额 |

## 组件事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| debt-more | 债权抵扣更多按钮点击 | - |
| cash-more | 现金券更多按钮点击 | - |
| commission-more | 佣金支付更多按钮点击 | - |
| points-more | 添星积分更多按钮点击 | - |

## 使用示例

```vue
<template>
  <view>
    <PriceDetail 
      :productAmount="orderInfo.productAmount"
      :shippingFee="orderInfo.shippingFee"
      :debtDeduction="orderInfo.debtDeduction"
      :cashCoupon="orderInfo.cashCoupon"
      :commissionPayment="orderInfo.commissionPayment"
      :pointsDeduction="orderInfo.pointsDeduction"
      :totalAmount="orderInfo.totalAmount"
      @debt-more="handleDebtMore"
      @cash-more="handleCashMore"
      @commission-more="handleCommissionMore"
      @points-more="handlePointsMore"
    />
  </view>
</template>

<script>
import PriceDetail from './components/PriceDetail.vue'

export default {
  components: {
    PriceDetail
  },
  data() {
    return {
      orderInfo: {
        productAmount: '1924.50',
        shippingFee: '3.50',
        debtDeduction: '192.40',
        cashCoupon: '0.0',
        commissionPayment: '0.0',
        pointsDeduction: '0.0',
        totalAmount: '1735.60'
      }
    }
  },
  methods: {
    handleDebtMore() {
      console.log('债权抵扣更多');
    },
    handleCashMore() {
      console.log('现金券更多');
    },
    handleCommissionMore() {
      console.log('佣金支付更多');
    },
    handlePointsMore() {
      console.log('添星积分更多');
    }
  }
}
</script>
```

## 设计规范

- 背景色：#ffffff
- 圆角：16rpx
- 内边距：32rpx
- 字体：PingFang SC
- 主要文字颜色：#333333
- 价格颜色：#ff0000
- 图标尺寸：28rpx × 28rpx
- 更多按钮图标：20rpx × 20rpx

## 图标资源

组件使用的图标资源来自 Figma 设计稿：
- 债权抵扣图标
- 现金券图标  
- 佣金支付图标
- 添星积分图标
- 更多按钮图标

## 注意事项

1. 确保项目支持 UniApp 框架
2. 图标资源需要网络访问权限
3. 组件已集成到 order_confirm 页面中
4. 支持数据双向绑定和事件监听
